<route lang="json5">
{
  style: {
    navigationBarTitleText: '学期异动申请',
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

/** 路由实例 */
const router = useRouter()

/** 表单数据 */
const formData = ref({
  /** 异动申请学期 */
  semester: '2025-2026学年 第1学期',
  /** 异动类型 */
  changeType: '',
  /** 异动原因 */
  changeReason: '',
  /** 异动原因补充 */
  changeReasonDetails: '',
  /** 相关附件 */
  attachments: [] as string[],
})

/** 异动类型选项 */
const changeTypeOptions = ref([
  { label: '休学', value: 'suspend' },
  { label: '复学', value: 'resume' },
  { label: '退学', value: 'withdraw' },
  { label: '转专业', value: 'transfer' },
  { label: '转学', value: 'change_school' },
])

/** 异动原因选项 */
const changeReasonOptions = ref([
  { label: '疾病', value: 'illness' },
  { label: '家庭经济困难', value: 'financial' },
  { label: '个人发展需要', value: 'personal' },
  { label: '其他', value: 'other' },
])

/** 学期选择器显示状态 */
const showSemesterPicker = ref(false)

/** 异动类型选择器显示状态 */
const showChangeTypePicker = ref(false)

/** 异动原因选择器显示状态 */
const showChangeReasonPicker = ref(false)

/** 学期选项 */
const semesterOptions = ref([
  '2024-2025学年 第1学期',
  '2024-2025学年 第2学期',
  '2025-2026学年 第1学期',
  '2025-2026学年 第2学期',
])

/** 选择学期 */
const selectSemester = (semester: string) => {
  formData.value.semester = semester
  showSemesterPicker.value = false
}

/** 选择异动类型 */
const selectChangeType = (type: { label: string; value: string }) => {
  formData.value.changeType = type.label
  showChangeTypePicker.value = false
}

/** 选择异动原因 */
const selectChangeReason = (reason: { label: string; value: string }) => {
  formData.value.changeReason = reason.label
  showChangeReasonPicker.value = false
}

/** 选择附件 */
const selectAttachment = () => {
  uni.chooseImage({
    count: 9,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      formData.value.attachments.push(...res.tempFilePaths)
    },
  })
}

/** 删除附件 */
const removeAttachment = (index: number) => {
  formData.value.attachments.splice(index, 1)
}

/** 预览附件 */
const previewAttachment = (url: string) => {
  uni.previewImage({
    urls: [url],
    current: url,
  })
}

/** 提交申请 */
const submitApplication = () => {
  // 表单验证
  if (!formData.value.changeType) {
    uni.showToast({
      title: '请选择异动类型',
      icon: 'none',
    })
    return
  }

  if (!formData.value.changeReason) {
    uni.showToast({
      title: '请选择异动原因',
      icon: 'none',
    })
    return
  }

  // TODO: 调用提交接口
  console.log('提交申请', formData.value)

  uni.showToast({
    title: '申请提交成功',
    icon: 'success',
  })

  // 返回列表页
  setTimeout(() => {
    router.back()
  }, 1500)
}

/** 返回 */
const goBack = () => {
  router.back()
}

onMounted(() => {
  // 页面初始化
})
</script>

<template>
  <view class="apply-container bg-[#f2f2f7] min-h-screen">
    <!-- 表单内容 -->
    <view class="form-container p-4">
      <!-- 异动申请学期 -->
      <view class="form-item bg-white rounded-lg p-4 mb-4">
        <view class="form-label text-sm text-gray-600 mb-2">异动申请学期</view>
        <view
          class="form-input flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          @click="showSemesterPicker = true"
        >
          <text class="text-base">{{ formData.semester }}</text>
          <wd-icon name="arrow-right" custom-style="color: #999; font-size: 16px;" />
        </view>
      </view>

      <!-- 异动类型 -->
      <view class="form-item bg-white rounded-lg p-4 mb-4">
        <view class="form-label text-sm text-gray-600 mb-2">
          异动类型
          <text class="text-red-500">*</text>
        </view>
        <view
          class="form-input flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          @click="showChangeTypePicker = true"
        >
          <text class="text-base" :class="{ 'text-gray-400': !formData.changeType }">
            {{ formData.changeType || '请选择异动类型' }}
          </text>
          <wd-icon name="arrow-right" custom-style="color: #999; font-size: 16px;" />
        </view>
      </view>

      <!-- 异动原因 -->
      <view class="form-item bg-white rounded-lg p-4 mb-4">
        <view class="form-label text-sm text-gray-600 mb-2">
          异动原因
          <text class="text-red-500">*</text>
        </view>
        <view
          class="form-input flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          @click="showChangeReasonPicker = true"
        >
          <text class="text-base" :class="{ 'text-gray-400': !formData.changeReason }">
            {{ formData.changeReason || '请选择异动原因' }}
          </text>
          <wd-icon name="arrow-right" custom-style="color: #999; font-size: 16px;" />
        </view>
      </view>

      <!-- 异动原因补充 -->
      <view class="form-item bg-white rounded-lg p-4 mb-4">
        <view class="form-label text-sm text-gray-600 mb-2">异动原因补充</view>
        <textarea
          v-model="formData.changeReasonDetails"
          class="form-textarea w-full p-3 bg-gray-50 rounded-lg text-base"
          placeholder="请详细说明异动原因..."
          :maxlength="500"
          :show-count="true"
          :auto-height="true"
        />
      </view>

      <!-- 相关附件 -->
      <view class="form-item bg-white rounded-lg p-4 mb-4">
        <view class="form-label text-sm text-gray-600 mb-2">相关附件</view>
        <view class="attachment-container">
          <!-- 已选择的附件 -->
          <view
            v-if="formData.attachments.length > 0"
            class="attachment-list grid grid-cols-3 gap-2 mb-3"
          >
            <view
              v-for="(attachment, index) in formData.attachments"
              :key="index"
              class="attachment-item relative"
            >
              <image
                :src="attachment"
                class="w-full h-20 rounded-lg object-cover"
                @click="previewAttachment(attachment)"
              />
              <view
                class="attachment-remove absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center"
                @click="removeAttachment(index)"
              >
                <wd-icon name="close" custom-style="color: white; font-size: 12px;" />
              </view>
            </view>
          </view>

          <!-- 添加附件按钮 -->
          <view
            class="add-attachment flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg"
            @click="selectAttachment"
          >
            <view class="text-center">
              <wd-icon
                name="add"
                custom-style="color: #999; font-size: 24px; margin-bottom: 8px;"
              />
              <text class="text-sm text-gray-500">添加附件</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view
      class="bottom-actions fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-100"
    >
      <view class="flex gap-3">
        <button
          class="flex-1 py-3 bg-gray-100 text-gray-600 rounded-lg text-center"
          @click="goBack"
        >
          取消
        </button>
        <button
          class="flex-1 py-3 bg-blue-500 text-white rounded-lg text-center"
          @click="submitApplication"
        >
          提交申请
        </button>
      </view>
    </view>

    <!-- 学期选择器 -->
    <wd-popup v-model="showSemesterPicker" position="bottom">
      <view class="picker-container bg-white">
        <view class="picker-header flex items-center justify-between p-4 border-b border-gray-100">
          <text class="text-lg font-semibold">选择学期</text>
          <wd-icon name="close" @click="showSemesterPicker = false" />
        </view>
        <view class="picker-content">
          <view
            v-for="semester in semesterOptions"
            :key="semester"
            class="picker-item p-4 border-b border-gray-50"
            @click="selectSemester(semester)"
          >
            <text class="text-base">{{ semester }}</text>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 异动类型选择器 -->
    <wd-popup v-model="showChangeTypePicker" position="bottom">
      <view class="picker-container bg-white">
        <view class="picker-header flex items-center justify-between p-4 border-b border-gray-100">
          <text class="text-lg font-semibold">选择异动类型</text>
          <wd-icon name="close" @click="showChangeTypePicker = false" />
        </view>
        <view class="picker-content">
          <view
            v-for="type in changeTypeOptions"
            :key="type.value"
            class="picker-item p-4 border-b border-gray-50"
            @click="selectChangeType(type)"
          >
            <text class="text-base">{{ type.label }}</text>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 异动原因选择器 -->
    <wd-popup v-model="showChangeReasonPicker" position="bottom">
      <view class="picker-container bg-white">
        <view class="picker-header flex items-center justify-between p-4 border-b border-gray-100">
          <text class="text-lg font-semibold">选择异动原因</text>
          <wd-icon name="close" @click="showChangeReasonPicker = false" />
        </view>
        <view class="picker-content">
          <view
            v-for="reason in changeReasonOptions"
            :key="reason.value"
            class="picker-item p-4 border-b border-gray-50"
            @click="selectChangeReason(reason)"
          >
            <text class="text-base">{{ reason.label }}</text>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
.apply-container {
  padding-bottom: 80px; // 为底部按钮留出空间
}

.form-textarea {
  min-height: 80px;
  resize: none;
  border: none;
  outline: none;
}

.attachment-item {
  &:hover .attachment-remove {
    opacity: 1;
  }
}

.attachment-remove {
  opacity: 0.8;
  transition: opacity 0.2s;
}

.add-attachment {
  min-height: 80px;
  cursor: pointer;
  transition: border-color 0.2s;

  &:hover {
    border-color: #0083ff;
  }
}

.picker-item {
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}
</style>
